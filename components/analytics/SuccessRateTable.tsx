// Success Rate Table Component - Story 4 Phase 3

'use client'

import { useState } from 'react'
import { 
  ChevronUpIcon, 
  ChevronDownIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  TrendingUpIcon,
  TrendingDownIcon
} from 'lucide-react'
import { SuccessRateAnalysis, AnalyticsTimeRange } from '@/types/analytics'

interface SuccessRateTableProps {
  data: SuccessRateAnalysis
  timeRange: AnalyticsTimeRange
  className?: string
}

type SortField = 'name' | 'successRate' | 'totalConversations' | 'successfulConversations' | 'trend'
type SortDirection = 'asc' | 'desc'

export function SuccessRateTable({ 
  data, 
  timeRange, 
  className = '' 
}: SuccessRateTableProps) {
  const [sortField, setSortField] = useState<SortField>('successRate')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState<'all' | 'high' | 'medium' | 'low'>('all')

  // Erweiterte Agent-Daten mit Mock-Trends
  const agentData = data.byAgent?.map(agent => ({
    ...agent,
    name: agent.name || `Agent ${agent.id}`,
    trend: Math.random() * 10 - 5, // Mock-Trend zwischen -5% und +5%
    previousSuccessRate: agent.successRate + (Math.random() * 10 - 5), // Mock vorherige Rate
    improvement: Math.random() * 20 - 10 // Mock Verbesserung
  })) || []

  // Filtering
  const filteredData = agentData.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchTerm.toLowerCase())
    
    let matchesFilter = true
    switch (filterCategory) {
      case 'high':
        matchesFilter = agent.successRate >= 90
        break
      case 'medium':
        matchesFilter = agent.successRate >= 80 && agent.successRate < 90
        break
      case 'low':
        matchesFilter = agent.successRate < 80
        break
    }
    
    return matchesSearch && matchesFilter
  })

  // Sorting
  const sortedData = [...filteredData].sort((a, b) => {
    let aValue: any
    let bValue: any
    
    switch (sortField) {
      case 'name':
        aValue = a.name
        bValue = b.name
        break
      case 'successRate':
        aValue = a.successRate
        bValue = b.successRate
        break
      case 'totalConversations':
        aValue = a.totalConversations
        bValue = b.totalConversations
        break
      case 'successfulConversations':
        aValue = a.successfulConversations
        bValue = b.successfulConversations
        break
      case 'trend':
        aValue = a.trend
        bValue = b.trend
        break
      default:
        return 0
    }
    
    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  const SortIcon = ({ field }: { field: SortField }) => {
    if (sortField !== field) {
      return <div className="w-4 h-4" />
    }
    return sortDirection === 'asc' 
      ? <ChevronUpIcon className="w-4 h-4" />
      : <ChevronDownIcon className="w-4 h-4" />
  }

  const getSuccessRateColor = (rate: number): string => {
    if (rate >= 90) return 'text-green-600 bg-green-50'
    if (rate >= 80) return 'text-yellow-600 bg-yellow-50'
    return 'text-red-600 bg-red-50'
  }

  const getTrendColor = (trend: number): string => {
    if (trend > 0) return 'text-green-600'
    if (trend < 0) return 'text-red-600'
    return 'text-gray-600'
  }

  const TrendIndicator = ({ value }: { value: number }) => {
    const isPositive = value > 0
    const Icon = isPositive ? TrendingUpIcon : TrendingDownIcon
    const colorClass = getTrendColor(value)
    
    return (
      <div className={`flex items-center ${colorClass}`}>
        <Icon className="h-4 w-4 mr-1" />
        <span className="text-sm font-medium">
          {Math.abs(value).toFixed(1)}%
        </span>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <h3 className="text-lg font-semibold text-gray-900">
            Detaillierte Erfolgsrate-Analyse
          </h3>
          
          <div className="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
            {/* Suche */}
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Agent suchen..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            {/* Filter */}
            <div className="relative">
              <FunnelIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value as any)}
                className="pl-10 pr-8 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white"
              >
                <option value="all">Alle Kategorien</option>
                <option value="high">Hoch (≥90%)</option>
                <option value="medium">Mittel (80-89%)</option>
                <option value="low">Niedrig (&lt;80%)</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('name')}
              >
                <div className="flex items-center space-x-1">
                  <span>Agent</span>
                  <SortIcon field="name" />
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('successRate')}
              >
                <div className="flex items-center space-x-1">
                  <span>Erfolgsrate</span>
                  <SortIcon field="successRate" />
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('totalConversations')}
              >
                <div className="flex items-center space-x-1">
                  <span>Gespräche</span>
                  <SortIcon field="totalConversations" />
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('successfulConversations')}
              >
                <div className="flex items-center space-x-1">
                  <span>Erfolgreich</span>
                  <SortIcon field="successfulConversations" />
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('trend')}
              >
                <div className="flex items-center space-x-1">
                  <span>Trend</span>
                  <SortIcon field="trend" />
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Performance
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedData.map((agent, index) => (
              <tr key={agent.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-8 w-8">
                      <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <span className="text-sm font-medium text-blue-600">
                          {agent.name.charAt(0)}
                        </span>
                      </div>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">
                        {agent.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        Rang #{index + 1}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSuccessRateColor(agent.successRate)}`}>
                    {agent.successRate.toFixed(1)}%
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {agent.totalConversations.toLocaleString('de-DE')}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {agent.successfulConversations.toLocaleString('de-DE')}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <TrendIndicator value={agent.trend} />
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                      <div 
                        className={`h-2 rounded-full ${
                          agent.successRate >= 90 ? 'bg-green-500' :
                          agent.successRate >= 80 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${agent.successRate}%` }}
                      />
                    </div>
                    <span className="text-xs text-gray-500">
                      {agent.successRate.toFixed(0)}%
                    </span>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {sortedData.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">
            Keine Agents gefunden, die den Filterkriterien entsprechen.
          </p>
        </div>
      )}

      {/* Tabellen-Footer mit Zusammenfassung */}
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between text-sm text-gray-600">
          <div className="flex items-center space-x-4">
            <span>Zeige {sortedData.length} von {agentData.length} Agents</span>
            <span>•</span>
            <span>Ø Erfolgsrate: {(agentData.reduce((sum, a) => sum + a.successRate, 0) / agentData.length).toFixed(1)}%</span>
          </div>
          <div className="mt-2 sm:mt-0">
            Zeitraum: {timeRange.startDate.toLocaleDateString('de-DE')} - {timeRange.endDate.toLocaleDateString('de-DE')}
          </div>
        </div>
      </div>
    </div>
  )
}
