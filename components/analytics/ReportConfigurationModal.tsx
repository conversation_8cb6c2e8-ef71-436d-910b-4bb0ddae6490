// Report Configuration Modal - Story 4 Phase 2

'use client'

import { useState, useEffect } from 'react'
import { X, Calendar } from 'lucide-react'
import { AnalyticsQuery } from '@/types/analytics'

interface ReportConfigurationModalProps {
  currentQuery: AnalyticsQuery
  onSave: (query: AnalyticsQuery) => void
  onClose: () => void
}

export function ReportConfigurationModal({ 
  currentQuery, 
  onSave, 
  onClose 
}: ReportConfigurationModalProps) {
  const [formData, setFormData] = useState({
    startDate: currentQuery.timeRange.startDate.toISOString().split('T')[0],
    endDate: currentQuery.timeRange.endDate.toISOString().split('T')[0],
    period: currentQuery.timeRange.period,
    agentIds: currentQuery.agentIds || []
  })

  const [availableAgents, setAvailableAgents] = useState<Array<{ id: string; name: string }>>([])
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Mock agents - in real app würde das von API kommen
  useEffect(() => {
    setAvailableAgents([
      { id: 'agent-1', name: 'Agent Alpha' },
      { id: 'agent-2', name: 'Agent Beta' },
      { id: 'agent-3', name: 'Agent Gamma' },
      { id: 'agent-4', name: 'Agent Delta' }
    ])
  }, [])

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    // Datum-Validierung
    const startDate = new Date(formData.startDate)
    const endDate = new Date(formData.endDate)
    const now = new Date()

    if (startDate >= endDate) {
      newErrors.dateRange = 'Startdatum muss vor dem Enddatum liegen'
    }

    if (endDate > now) {
      newErrors.endDate = 'Enddatum kann nicht in der Zukunft liegen'
    }

    // Maximaler Zeitraum (1 Jahr)
    const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
    if (daysDiff > 365) {
      newErrors.dateRange = 'Zeitraum darf maximal 365 Tage betragen'
    }

    if (daysDiff < 1) {
      newErrors.dateRange = 'Zeitraum muss mindestens 1 Tag betragen'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    const newQuery: AnalyticsQuery = {
      timeRange: {
        startDate: new Date(formData.startDate),
        endDate: new Date(formData.endDate),
        period: formData.period as any
      },
      agentIds: formData.agentIds.length > 0 ? formData.agentIds : undefined
    }

    onSave(newQuery)
  }

  const handleAgentToggle = (agentId: string) => {
    setFormData(prev => ({
      ...prev,
      agentIds: prev.agentIds.includes(agentId)
        ? prev.agentIds.filter(id => id !== agentId)
        : [...prev.agentIds, agentId]
    }))
  }

  const handlePresetRange = (preset: string) => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    
    let startDate: Date
    let endDate: Date
    let period: string

    switch (preset) {
      case 'today':
        startDate = today
        endDate = now
        period = 'daily'
        break
      case 'yesterday':
        startDate = new Date(today.getTime() - 24 * 60 * 60 * 1000)
        endDate = today
        period = 'daily'
        break
      case 'last7days':
        startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
        endDate = now
        period = 'weekly'
        break
      case 'last30days':
        startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
        endDate = now
        period = 'monthly'
        break
      case 'last90days':
        startDate = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000)
        endDate = now
        period = 'monthly'
        break
      default:
        return
    }

    setFormData(prev => ({
      ...prev,
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
      period
    }))
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex items-center justify-between pb-4 border-b">
          <h3 className="text-lg font-semibold text-gray-900">
            Report-Konfiguration
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="mt-6 space-y-6">
          {/* Zeitraum-Presets */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Schnellauswahl
            </label>
            <div className="flex flex-wrap gap-2">
              {[
                { key: 'today', label: 'Heute' },
                { key: 'yesterday', label: 'Gestern' },
                { key: 'last7days', label: 'Letzte 7 Tage' },
                { key: 'last30days', label: 'Letzte 30 Tage' },
                { key: 'last90days', label: 'Letzte 90 Tage' }
              ].map(({ key, label }) => (
                <button
                  key={key}
                  type="button"
                  onClick={() => handlePresetRange(key)}
                  className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {label}
                </button>
              ))}
            </div>
          </div>

          {/* Datum-Bereich */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
                Startdatum
              </label>
              <div className="mt-1 relative">
                <input
                  type="date"
                  id="startDate"
                  value={formData.startDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
                <Calendar className="absolute right-3 top-2 h-5 w-5 text-gray-400 pointer-events-none" />
              </div>
            </div>

            <div>
              <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">
                Enddatum
              </label>
              <div className="mt-1 relative">
                <input
                  type="date"
                  id="endDate"
                  value={formData.endDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
                <Calendar className="absolute right-3 top-2 h-5 w-5 text-gray-400 pointer-events-none" />
              </div>
            </div>
          </div>

          {/* Fehler-Anzeige für Datumsbereich */}
          {(errors.dateRange || errors.endDate) && (
            <div className="text-red-600 text-sm">
              {errors.dateRange || errors.endDate}
            </div>
          )}

          {/* Periode */}
          <div>
            <label htmlFor="period" className="block text-sm font-medium text-gray-700">
              Aggregationsperiode
            </label>
            <select
              id="period"
              value={formData.period}
              onChange={(e) => setFormData(prev => ({ ...prev, period: e.target.value }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="daily">Täglich</option>
              <option value="weekly">Wöchentlich</option>
              <option value="monthly">Monatlich</option>
              <option value="custom">Benutzerdefiniert</option>
            </select>
          </div>

          {/* Agent-Auswahl */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Agents (optional)
            </label>
            <div className="space-y-2 max-h-40 overflow-y-auto border border-gray-200 rounded-md p-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.agentIds.length === 0}
                  onChange={() => setFormData(prev => ({ ...prev, agentIds: [] }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700 font-medium">
                  Alle Agents
                </span>
              </label>
              
              {availableAgents.map((agent) => (
                <label key={agent.id} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.agentIds.includes(agent.id)}
                    onChange={() => handleAgentToggle(agent.id)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    {agent.name}
                  </span>
                </label>
              ))}
            </div>
            {formData.agentIds.length > 0 && (
              <p className="mt-1 text-xs text-gray-500">
                {formData.agentIds.length} Agent(s) ausgewählt
              </p>
            )}
          </div>

          {/* Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Abbrechen
            </button>
            <button
              type="submit"
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Anwenden
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
