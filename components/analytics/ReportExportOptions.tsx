// Report Export Options Modal - Story 4 Phase 2

'use client'

import { useState } from 'react'
import {
  X,
  Download,
  FileText,
  Table,
  BarChart3
} from 'lucide-react'

interface ReportExportOptionsProps {
  onExport: (format: 'pdf' | 'excel' | 'csv', options?: ExportOptions) => void
  onClose: () => void
  loading?: boolean
}

interface ExportOptions {
  includeCharts: boolean
  includeRawData: boolean
  language: 'de' | 'en'
}

export function ReportExportOptions({ 
  onExport, 
  onClose, 
  loading = false 
}: ReportExportOptionsProps) {
  const [selectedFormat, setSelectedFormat] = useState<'pdf' | 'excel' | 'csv'>('pdf')
  const [options, setOptions] = useState<ExportOptions>({
    includeCharts: true,
    includeRawData: false,
    language: 'de'
  })

  const formatOptions = [
    {
      id: 'pdf',
      name: 'PDF',
      description: 'Professioneller Report mit Diagrammen und Formatierung',
      icon: FileText,
      features: ['Diagramme', 'Formatierung', 'Druckoptimiert'],
      recommended: true
    },
    {
      id: 'excel',
      name: 'Excel',
      description: 'Tabellenkalkulation mit Rohdaten und Pivot-Tabellen',
      icon: Table,
      features: ['Rohdaten', 'Pivot-Tabellen', 'Formeln'],
      recommended: false
    },
    {
      id: 'csv',
      name: 'CSV',
      description: 'Einfache Datentabelle für weitere Verarbeitung',
      icon: Download,
      features: ['Rohdaten', 'Universell', 'Kompakt'],
      recommended: false
    }
  ]

  const handleExport = () => {
    onExport(selectedFormat, options)
  }

  const updateOption = (key: keyof ExportOptions, value: any) => {
    setOptions(prev => ({ ...prev, [key]: value }))
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex items-center justify-between pb-4 border-b">
          <h3 className="text-lg font-semibold text-gray-900">
            Report exportieren
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={loading}
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="mt-6 space-y-6">
          {/* Format-Auswahl */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">
              Export-Format wählen
            </label>
            <div className="space-y-3">
              {formatOptions.map((format) => {
                const IconComponent = format.icon
                return (
                  <div
                    key={format.id}
                    className={`relative rounded-lg border p-4 cursor-pointer transition-colors ${
                      selectedFormat === format.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                    onClick={() => setSelectedFormat(format.id as any)}
                  >
                    <div className="flex items-start">
                      <div className="flex items-center h-5">
                        <input
                          type="radio"
                          name="format"
                          value={format.id}
                          checked={selectedFormat === format.id}
                          onChange={() => setSelectedFormat(format.id as any)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                      </div>
                      <div className="ml-3 flex-1">
                        <div className="flex items-center">
                          <IconComponent className="h-5 w-5 text-gray-400 mr-2" />
                          <span className="font-medium text-gray-900">
                            {format.name}
                          </span>
                          {format.recommended && (
                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                              Empfohlen
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-500 mt-1">
                          {format.description}
                        </p>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {format.features.map((feature) => (
                            <span
                              key={feature}
                              className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                            >
                              {feature}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Export-Optionen */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">
              Export-Optionen
            </label>
            <div className="space-y-4">
              {/* Diagramme einschließen */}
              {(selectedFormat === 'pdf' || selectedFormat === 'excel') && (
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="includeCharts"
                    checked={options.includeCharts}
                    onChange={(e) => updateOption('includeCharts', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="includeCharts" className="ml-2 text-sm text-gray-700">
                    <span className="font-medium">Diagramme einschließen</span>
                    <p className="text-gray-500">
                      Fügt visuelle Darstellungen der Daten hinzu
                    </p>
                  </label>
                </div>
              )}

              {/* Rohdaten einschließen */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="includeRawData"
                  checked={options.includeRawData}
                  onChange={(e) => updateOption('includeRawData', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="includeRawData" className="ml-2 text-sm text-gray-700">
                  <span className="font-medium">Rohdaten einschließen</span>
                  <p className="text-gray-500">
                    Fügt detaillierte Datentabellen hinzu
                  </p>
                </label>
              </div>

              {/* Sprache */}
              <div>
                <label htmlFor="language" className="block text-sm font-medium text-gray-700 mb-2">
                  Sprache
                </label>
                <select
                  id="language"
                  value={options.language}
                  onChange={(e) => updateOption('language', e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="de">Deutsch</option>
                  <option value="en">English</option>
                </select>
              </div>
            </div>
          </div>

          {/* Vorschau-Info */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-2">
              Export-Vorschau
            </h4>
            <div className="text-sm text-gray-600 space-y-1">
              <p>Format: <span className="font-medium">{selectedFormat.toUpperCase()}</span></p>
              <p>Sprache: <span className="font-medium">{options.language === 'de' ? 'Deutsch' : 'English'}</span></p>
              {options.includeCharts && (
                <p>✓ Diagramme werden eingeschlossen</p>
              )}
              {options.includeRawData && (
                <p>✓ Rohdaten werden eingeschlossen</p>
              )}
            </div>
          </div>

          {/* Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={onClose}
              disabled={loading}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              Abbrechen
            </button>
            <button
              type="button"
              onClick={handleExport}
              disabled={loading}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Wird exportiert...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export starten
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
