// Report Schedule Form Component - Story 4 Phase 2

'use client'

import { useState } from 'react'
import { 
  ClockIcon, 
  CalendarIcon,
  EnvelopeIcon,
  PlusIcon,
  XMarkIcon
} from 'lucide-react'
import { ScheduledReport } from '@/types/analytics'

interface ReportScheduleFormProps {
  onSubmit: (scheduleData: Omit<ScheduledReport, 'id' | 'createdAt' | 'updatedAt' | 'nextRun'>) => void
  onCancel: () => void
  initialData?: Partial<ScheduledReport>
  loading?: boolean
}

export function ReportScheduleForm({ 
  onSubmit, 
  onCancel, 
  initialData,
  loading = false 
}: ReportScheduleFormProps) {
  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    reportType: initialData?.reportType || 'periodic' as const,
    frequency: initialData?.schedule?.frequency || 'weekly' as const,
    time: initialData?.schedule?.time || '09:00',
    dayOfWeek: initialData?.schedule?.dayOfWeek || 1,
    dayOfMonth: initialData?.schedule?.dayOfMonth || 1,
    format: initialData?.config?.format || 'pdf' as const,
    language: initialData?.config?.language || 'de' as const,
    includeCharts: initialData?.config?.includeCharts ?? true,
    agentIds: initialData?.config?.agentIds || [],
    recipients: initialData?.recipients || [{ email: '', name: '' }],
    enabled: initialData?.enabled ?? true
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const reportTypes = [
    { value: 'periodic', label: 'Periodischer Report' },
    { value: 'success_rate', label: 'Erfolgsrate-Analyse' },
    { value: 'duration_stats', label: 'Gesprächsdauer-Statistiken' },
    { value: 'tool_usage', label: 'Tool-Nutzungs-Analytics' }
  ]

  const frequencies = [
    { value: 'daily', label: 'Täglich' },
    { value: 'weekly', label: 'Wöchentlich' },
    { value: 'monthly', label: 'Monatlich' }
  ]

  const weekdays = [
    { value: 1, label: 'Montag' },
    { value: 2, label: 'Dienstag' },
    { value: 3, label: 'Mittwoch' },
    { value: 4, label: 'Donnerstag' },
    { value: 5, label: 'Freitag' },
    { value: 6, label: 'Samstag' },
    { value: 0, label: 'Sonntag' }
  ]

  const formats = [
    { value: 'pdf', label: 'PDF' },
    { value: 'excel', label: 'Excel' },
    { value: 'csv', label: 'CSV' }
  ]

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Name ist erforderlich'
    }

    if (!formData.time) {
      newErrors.time = 'Uhrzeit ist erforderlich'
    }

    // Empfänger validieren
    const validRecipients = formData.recipients.filter(r => r.email.trim())
    if (validRecipients.length === 0) {
      newErrors.recipients = 'Mindestens ein Empfänger ist erforderlich'
    } else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      const invalidEmails = validRecipients.filter(r => !emailRegex.test(r.email))
      if (invalidEmails.length > 0) {
        newErrors.recipients = 'Ungültige E-Mail-Adressen gefunden'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    const validRecipients = formData.recipients.filter(r => r.email.trim())

    const scheduleData: Omit<ScheduledReport, 'id' | 'createdAt' | 'updatedAt' | 'nextRun'> = {
      name: formData.name.trim(),
      reportType: formData.reportType,
      schedule: {
        frequency: formData.frequency,
        time: formData.time,
        ...(formData.frequency === 'weekly' && { dayOfWeek: formData.dayOfWeek }),
        ...(formData.frequency === 'monthly' && { dayOfMonth: formData.dayOfMonth })
      },
      config: {
        format: formData.format,
        language: formData.language,
        includeCharts: formData.includeCharts,
        agentIds: formData.agentIds.length > 0 ? formData.agentIds : undefined
      },
      recipients: validRecipients,
      enabled: formData.enabled
    }

    onSubmit(scheduleData)
  }

  const addRecipient = () => {
    setFormData(prev => ({
      ...prev,
      recipients: [...prev.recipients, { email: '', name: '' }]
    }))
  }

  const removeRecipient = (index: number) => {
    setFormData(prev => ({
      ...prev,
      recipients: prev.recipients.filter((_, i) => i !== index)
    }))
  }

  const updateRecipient = (index: number, field: 'email' | 'name', value: string) => {
    setFormData(prev => ({
      ...prev,
      recipients: prev.recipients.map((recipient, i) => 
        i === index ? { ...recipient, [field]: value } : recipient
      )
    }))
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Grundeinstellungen */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Grundeinstellungen
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              Report-Name *
            </label>
            <input
              type="text"
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="z.B. Wöchentlicher Performance Report"
            />
            {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
          </div>

          <div>
            <label htmlFor="reportType" className="block text-sm font-medium text-gray-700">
              Report-Typ
            </label>
            <select
              id="reportType"
              value={formData.reportType}
              onChange={(e) => setFormData(prev => ({ ...prev, reportType: e.target.value as any }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              {reportTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="mt-4 flex items-center">
          <input
            type="checkbox"
            id="enabled"
            checked={formData.enabled}
            onChange={(e) => setFormData(prev => ({ ...prev, enabled: e.target.checked }))}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="enabled" className="ml-2 text-sm text-gray-700">
            Report aktiviert
          </label>
        </div>
      </div>

      {/* Zeitplan */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <CalendarIcon className="h-5 w-5 mr-2" />
          Zeitplan
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="frequency" className="block text-sm font-medium text-gray-700">
              Häufigkeit
            </label>
            <select
              id="frequency"
              value={formData.frequency}
              onChange={(e) => setFormData(prev => ({ ...prev, frequency: e.target.value as any }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              {frequencies.map(freq => (
                <option key={freq.value} value={freq.value}>
                  {freq.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="time" className="block text-sm font-medium text-gray-700">
              Uhrzeit *
            </label>
            <div className="mt-1 relative">
              <input
                type="time"
                id="time"
                value={formData.time}
                onChange={(e) => setFormData(prev => ({ ...prev, time: e.target.value }))}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              <ClockIcon className="absolute right-3 top-2 h-5 w-5 text-gray-400 pointer-events-none" />
            </div>
            {errors.time && <p className="mt-1 text-sm text-red-600">{errors.time}</p>}
          </div>

          {formData.frequency === 'weekly' && (
            <div>
              <label htmlFor="dayOfWeek" className="block text-sm font-medium text-gray-700">
                Wochentag
              </label>
              <select
                id="dayOfWeek"
                value={formData.dayOfWeek}
                onChange={(e) => setFormData(prev => ({ ...prev, dayOfWeek: parseInt(e.target.value) }))}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                {weekdays.map(day => (
                  <option key={day.value} value={day.value}>
                    {day.label}
                  </option>
                ))}
              </select>
            </div>
          )}

          {formData.frequency === 'monthly' && (
            <div>
              <label htmlFor="dayOfMonth" className="block text-sm font-medium text-gray-700">
                Tag des Monats
              </label>
              <select
                id="dayOfMonth"
                value={formData.dayOfMonth}
                onChange={(e) => setFormData(prev => ({ ...prev, dayOfMonth: parseInt(e.target.value) }))}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                {Array.from({ length: 31 }, (_, i) => i + 1).map(day => (
                  <option key={day} value={day}>
                    {day}.
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      </div>

      {/* Export-Einstellungen */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Export-Einstellungen
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="format" className="block text-sm font-medium text-gray-700">
              Format
            </label>
            <select
              id="format"
              value={formData.format}
              onChange={(e) => setFormData(prev => ({ ...prev, format: e.target.value as any }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              {formats.map(format => (
                <option key={format.value} value={format.value}>
                  {format.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="language" className="block text-sm font-medium text-gray-700">
              Sprache
            </label>
            <select
              id="language"
              value={formData.language}
              onChange={(e) => setFormData(prev => ({ ...prev, language: e.target.value as any }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="de">Deutsch</option>
              <option value="en">English</option>
            </select>
          </div>

          <div className="flex items-center pt-6">
            <input
              type="checkbox"
              id="includeCharts"
              checked={formData.includeCharts}
              onChange={(e) => setFormData(prev => ({ ...prev, includeCharts: e.target.checked }))}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="includeCharts" className="ml-2 text-sm text-gray-700">
              Diagramme einschließen
            </label>
          </div>
        </div>
      </div>

      {/* Empfänger */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <EnvelopeIcon className="h-5 w-5 mr-2" />
            Empfänger
          </h3>
          <button
            type="button"
            onClick={addRecipient}
            className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <PlusIcon className="h-4 w-4 mr-1" />
            Hinzufügen
          </button>
        </div>

        <div className="space-y-3">
          {formData.recipients.map((recipient, index) => (
            <div key={index} className="flex gap-3">
              <div className="flex-1">
                <input
                  type="email"
                  placeholder="E-Mail-Adresse *"
                  value={recipient.email}
                  onChange={(e) => updateRecipient(index, 'email', e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="Name (optional)"
                  value={recipient.name}
                  onChange={(e) => updateRecipient(index, 'name', e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              {formData.recipients.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeRecipient(index)}
                  className="px-3 py-2 text-red-600 hover:text-red-800"
                >
                  <XMarkIcon className="h-5 w-5" />
                </button>
              )}
            </div>
          ))}
        </div>
        {errors.recipients && <p className="mt-2 text-sm text-red-600">{errors.recipients}</p>}
      </div>

      {/* Buttons */}
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          disabled={loading}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          Abbrechen
        </button>
        <button
          type="submit"
          disabled={loading}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Wird gespeichert...
            </>
          ) : (
            'Zeitplan speichern'
          )}
        </button>
      </div>
    </form>
  )
}
