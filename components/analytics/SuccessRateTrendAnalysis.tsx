// Success Rate Trend Analysis Component - Story 4 Phase 3

'use client'

import { 
  <PERSON><PERSON>hart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON>ltip, 
  Legend, 
  ResponsiveContainer,
  ReferenceLine,
  ComposedChart,
  Bar
} from 'recharts'
import { 
  TrendingUpIcon, 
  TrendingDownIcon,
  MinusIcon,
  AlertTriangle,
  CheckCircleIcon,
  ClockIcon
} from 'lucide-react'
import { SuccessRateAnalysis, AnalyticsTimeRange } from '@/types/analytics'

interface SuccessRateTrendAnalysisProps {
  data: SuccessRateAnalysis
  timeRange: AnalyticsTimeRange
  className?: string
}

export function SuccessRateTrendAnalysis({ 
  data, 
  timeRange, 
  className = '' 
}: SuccessRateTrendAnalysisProps) {
  
  // Mock-Trend-Daten für die letzten 30 Tage
  const generateTrendData = () => {
    const days = []
    const startDate = new Date(timeRange.startDate)
    const endDate = new Date(timeRange.endDate)
    
    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const dayOfWeek = d.getDay()
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6
      
      // Simuliere realistische Erfolgsraten mit Wochenend-Effekt
      const baseRate = data.overallSuccessRate
      const weekendPenalty = isWeekend ? -5 : 0
      const randomVariation = (Math.random() - 0.5) * 10
      const successRate = Math.max(70, Math.min(100, baseRate + weekendPenalty + randomVariation))
      
      // Simuliere Gesprächsvolumen
      const baseVolume = isWeekend ? 20 : 50
      const volumeVariation = Math.random() * 30
      const conversations = Math.round(baseVolume + volumeVariation)
      
      days.push({
        datum: d.toLocaleDateString('de-DE', { month: 'short', day: 'numeric' }),
        erfolgsrate: Number(successRate.toFixed(1)),
        gespräche: conversations,
        ziel: 90,
        isWeekend,
        fullDate: new Date(d)
      })
    }
    
    return days
  }

  const trendData = generateTrendData()
  
  // Trend-Analyse
  const analyzeTrend = () => {
    if (trendData.length < 7) return { direction: 'stable', change: 0, confidence: 'low' }
    
    const recent = trendData.slice(-7).reduce((sum, d) => sum + d.erfolgsrate, 0) / 7
    const previous = trendData.slice(-14, -7).reduce((sum, d) => sum + d.erfolgsrate, 0) / 7
    const change = recent - previous
    
    let direction: 'up' | 'down' | 'stable' = 'stable'
    if (Math.abs(change) > 2) {
      direction = change > 0 ? 'up' : 'down'
    }
    
    const confidence = Math.abs(change) > 5 ? 'high' : Math.abs(change) > 2 ? 'medium' : 'low'
    
    return { direction, change, confidence }
  }

  const trend = analyzeTrend()
  
  // Wochentagsanalyse
  const weekdayAnalysis = () => {
    const weekdays = ['Sonntag', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag']
    const weekdayData = weekdays.map((name, index) => {
      const dayData = trendData.filter(d => d.fullDate.getDay() === index)
      const avgRate = dayData.length > 0 
        ? dayData.reduce((sum, d) => sum + d.erfolgsrate, 0) / dayData.length
        : 0
      const totalConversations = dayData.reduce((sum, d) => sum + d.gespräche, 0)
      
      return {
        wochentag: name,
        erfolgsrate: Number(avgRate.toFixed(1)),
        gespräche: totalConversations,
        isWeekend: index === 0 || index === 6
      }
    })
    
    return weekdayData
  }

  const weekdayData = weekdayAnalysis()

  // Custom Tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value}
              {entry.name.includes('rate') ? '%' : ''}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  const TrendIcon = ({ direction }: { direction: 'up' | 'down' | 'stable' }) => {
    switch (direction) {
      case 'up':
        return <TrendingUpIcon className="h-5 w-5 text-green-600" />
      case 'down':
        return <TrendingDownIcon className="h-5 w-5 text-red-600" />
      default:
        return <MinusIcon className="h-5 w-5 text-gray-600" />
    }
  }

  const getTrendColor = (direction: string) => {
    switch (direction) {
      case 'up': return 'text-green-600 bg-green-50 border-green-200'
      case 'down': return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Trend-Übersicht */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">
          Erfolgsrate-Trend-Analyse
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Aktueller Trend */}
          <div className={`border rounded-lg p-4 ${getTrendColor(trend.direction)}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <TrendIcon direction={trend.direction} />
                <div className="ml-3">
                  <p className="text-sm font-medium">
                    7-Tage-Trend
                  </p>
                  <p className="text-2xl font-bold">
                    {trend.change > 0 ? '+' : ''}{trend.change.toFixed(1)}%
                  </p>
                </div>
              </div>
              <div className="text-xs font-medium uppercase">
                {trend.confidence === 'high' ? 'Stark' : 
                 trend.confidence === 'medium' ? 'Moderat' : 'Schwach'}
              </div>
            </div>
          </div>

          {/* Beste Performance */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <CheckCircleIcon className="h-5 w-5 text-green-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-green-800">
                  Beste Tagesleistung
                </p>
                <p className="text-2xl font-bold text-green-900">
                  {Math.max(...trendData.map(d => d.erfolgsrate)).toFixed(1)}%
                </p>
                <p className="text-sm text-green-700">
                  {trendData.find(d => d.erfolgsrate === Math.max(...trendData.map(d => d.erfolgsrate)))?.datum}
                </p>
              </div>
            </div>
          </div>

          {/* Verbesserungspotential */}
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center">
              <ExclamationTriangleIcon className="h-5 w-5 text-orange-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-orange-800">
                  Niedrigste Tagesleistung
                </p>
                <p className="text-2xl font-bold text-orange-900">
                  {Math.min(...trendData.map(d => d.erfolgsrate)).toFixed(1)}%
                </p>
                <p className="text-sm text-orange-700">
                  {trendData.find(d => d.erfolgsrate === Math.min(...trendData.map(d => d.erfolgsrate)))?.datum}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Täglicher Trend-Chart */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Tägliche Erfolgsrate-Entwicklung
        </h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="datum" 
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
              />
              <YAxis 
                yAxisId="rate"
                domain={[70, 100]}
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
              />
              <YAxis 
                yAxisId="count"
                orientation="right"
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              
              {/* Zielbereich */}
              <ReferenceLine 
                yAxisId="rate"
                y={90} 
                stroke="#F59E0B" 
                strokeDasharray="5 5"
                label={{ value: "Ziel 90%", position: "topRight" }}
              />
              
              {/* Gespräche als Balken */}
              <Bar 
                yAxisId="count"
                dataKey="gespräche" 
                fill="#6B7280"
                opacity={0.3}
                name="Gespräche"
              />
              
              {/* Erfolgsrate als Linie */}
              <Line 
                yAxisId="rate"
                type="monotone" 
                dataKey="erfolgsrate" 
                stroke="#10B981"
                strokeWidth={3}
                dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                name="Erfolgsrate (%)"
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Wochentagsanalyse */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Erfolgsrate nach Wochentag
        </h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={weekdayData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="wochentag" 
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
              />
              <YAxis 
                yAxisId="rate"
                domain={[70, 100]}
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
              />
              <YAxis 
                yAxisId="count"
                orientation="right"
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              
              {/* Gespräche als Balken */}
              <Bar 
                yAxisId="count"
                dataKey="gespräche" 
                fill="#6B7280"
                opacity={0.3}
                name="Gespräche"
              />
              
              {/* Erfolgsrate als Linie */}
              <Line 
                yAxisId="rate"
                type="monotone" 
                dataKey="erfolgsrate" 
                stroke="#3B82F6"
                strokeWidth={3}
                dot={(props: any) => {
                  const { payload } = props
                  return (
                    <circle
                      cx={props.cx}
                      cy={props.cy}
                      r={5}
                      fill={payload.isWeekend ? '#EF4444' : '#3B82F6'}
                      strokeWidth={2}
                      stroke="#fff"
                    />
                  )
                }}
                name="Erfolgsrate (%)"
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>
        
        <div className="mt-4 flex items-center justify-center space-x-6 text-sm">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-600 rounded-full mr-2"></div>
            <span>Werktage</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-red-600 rounded-full mr-2"></div>
            <span>Wochenende</span>
          </div>
        </div>
      </div>

      {/* Insights und Empfehlungen */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-4 flex items-center">
          <ClockIcon className="h-5 w-5 mr-2" />
          Trend-Insights & Empfehlungen
        </h3>
        
        <div className="space-y-3 text-sm text-blue-800">
          {trend.direction === 'up' && trend.confidence === 'high' && (
            <div className="flex items-start">
              <CheckCircleIcon className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
              <p className="text-green-800">
                <strong>Positive Entwicklung:</strong> Die Erfolgsrate zeigt einen starken Aufwärtstrend (+{trend.change.toFixed(1)}%). 
                Dokumentieren Sie die aktuellen Optimierungen für zukünftige Referenz.
              </p>
            </div>
          )}
          
          {trend.direction === 'down' && trend.confidence === 'high' && (
            <div className="flex items-start">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mr-2 mt-0.5" />
              <p className="text-red-800">
                <strong>Negative Entwicklung:</strong> Die Erfolgsrate fällt deutlich ({trend.change.toFixed(1)}%). 
                Überprüfen Sie Agent-Konfigurationen und aktuelle Änderungen.
              </p>
            </div>
          )}
          
          {weekdayData.some(d => d.isWeekend && d.erfolgsrate < weekdayData.filter(d => !d.isWeekend).reduce((sum, d) => sum + d.erfolgsrate, 0) / weekdayData.filter(d => !d.isWeekend).length - 10) && (
            <div className="flex items-start">
              <ExclamationTriangleIcon className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
              <p>
                <strong>Wochenend-Effekt:</strong> Deutlich niedrigere Erfolgsraten am Wochenende. 
                Erwägen Sie spezielle Wochenend-Konfigurationen oder reduzierte Verfügbarkeit.
              </p>
            </div>
          )}
          
          {Math.max(...trendData.map(d => d.erfolgsrate)) - Math.min(...trendData.map(d => d.erfolgsrate)) > 20 && (
            <div className="flex items-start">
              <ExclamationTriangleIcon className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
              <p>
                <strong>Hohe Volatilität:</strong> Große Schwankungen in der täglichen Performance 
                ({(Math.max(...trendData.map(d => d.erfolgsrate)) - Math.min(...trendData.map(d => d.erfolgsrate))).toFixed(1)}% Spannweite). 
                Analysieren Sie externe Faktoren und Systemstabilität.
              </p>
            </div>
          )}
          
          {data.overallSuccessRate >= 90 && trend.direction !== 'down' && (
            <div className="flex items-start">
              <CheckCircleIcon className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
              <p className="text-green-800">
                <strong>Exzellente Performance:</strong> Erfolgsrate über 90% mit stabilem oder positivem Trend. 
                Fokus auf Konsistenz und Skalierung der Best Practices.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
